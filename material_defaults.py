"""
القيم الافتراضية للمواد العلفية الشائعة
هذه القيم تقريبية ويمكن تعديلها حسب المصدر والجودة
"""

MATERIAL_DEFAULTS = {
    'برسيم حجازي': {
        'fiber': 25.0,      # نسبة الألياف %
        'protein': 18.0,    # نسبة البروتين %
        'energy': 2.2,      # الطاقة ميجا كالوري/كغ
        'price': 3.5        # السعر التقريبي دينار/كغ
    },
    'شعير': {
        'fiber': 5.5,
        'protein': 12.0,
        'energy': 2.8,
        'price': 2.2
    },
    'ذرة صفراء': {
        'fiber': 2.5,
        'protein': 9.0,
        'energy': 3.2,
        'price': 2.8
    },
    'نخالة قمح': {
        'fiber': 12.0,
        'protein': 16.0,
        'energy': 2.0,
        'price': 1.8
    },
    'كسبة فول الصويا': {
        'fiber': 6.0,
        'protein': 44.0,
        'energy': 2.3,
        'price': 4.5
    },
    'كسبة بذرة القطن': {
        'fiber': 12.0,
        'protein': 38.0,
        'energy': 2.0,
        'price': 3.2
    },
    'دريس رودس': {
        'fiber': 35.0,
        'protein': 8.0,
        'energy': 1.8,
        'price': 1.5
    },
    'تبن قمح': {
        'fiber': 45.0,
        'protein': 4.0,
        'energy': 1.2,
        'price': 0.8
    },
    'علف مركز': {
        'fiber': 8.0,
        'protein': 16.0,
        'energy': 2.5,
        'price': 3.0
    },
    'ملح طعام': {
        'fiber': 0.0,
        'protein': 0.0,
        'energy': 0.0,
        'price': 1.0
    },
    'حجر جيري': {
        'fiber': 0.0,
        'protein': 0.0,
        'energy': 0.0,
        'price': 0.5
    }
}

def get_material_defaults(material_name):
    """
    إرجاع القيم الافتراضية لمادة معينة
    """
    return MATERIAL_DEFAULTS.get(material_name, {
        'fiber': 0.0,
        'protein': 0.0,
        'energy': 0.0,
        'price': 0.0
    })

def get_all_materials_with_defaults():
    """
    إرجاع جميع المواد مع قيمها الافتراضية
    """
    return MATERIAL_DEFAULTS
