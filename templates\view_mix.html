<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تفاصيل الخلطة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body class="bg-light">
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <a href="{{ url_for('index') }}" class="btn btn-secondary">&larr; رجوع</a>
        <a href="{{ url_for('edit_mix', mix_id=mix.id) }}" class="btn btn-warning">✏️ تعديل الخلطة</a>
    </div>
    <h2>تفاصيل الخلطة: {{ mix.name }}</h2>
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">الألياف</h5>
                    <p class="card-text h4 text-primary">{{ '%.2f'|format(mix.total_fiber) }}%</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">البروتين</h5>
                    <p class="card-text h4 text-success">{{ '%.2f'|format(mix.total_protein) }}%</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">الطاقة</h5>
                    <p class="card-text h4 text-warning">{{ '%.2f'|format(mix.total_energy) }} م.ك/كغ</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">التكلفة الإجمالية</h5>
                    <p class="card-text h4 text-danger">{{ '%.2f'|format(mix.total_cost) }} د.أ</p>
                </div>
            </div>
        </div>
    </div>
    <h4 class="mb-3">تفاصيل المكونات</h4>
    <table class="table table-bordered table-striped">
        <thead class="table-dark">
            <tr>
                <th>المكون</th>
                <th>الكمية (كغ)</th>
                <th>الألياف (%)</th>
                <th>البروتين (%)</th>
                <th>الطاقة (م.ك/كغ)</th>
                <th>السعر/كغ (دينار)</th>
                <th>التكلفة الجزئية (دينار)</th>
            </tr>
        </thead>
        <tbody>
            {% for item in items %}
            <tr>
                <td><strong>{{ item.name }}</strong></td>
                <td>{{ '%.2f'|format(item.qty) }}</td>
                <td>{{ '%.2f'|format(item.fiber) }}</td>
                <td>{{ '%.2f'|format(item.protein) }}</td>
                <td>{{ '%.2f'|format(item.energy) }}</td>
                <td>{{ '%.2f'|format(item.price) }}</td>
                <td class="text-success"><strong>{{ '%.2f'|format(item.qty * item.price) }}</strong></td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="table-secondary">
            <tr>
                <th>المجموع</th>
                <th>{{ '%.2f'|format(items|sum(attribute='qty')) }} كغ</th>
                <th colspan="3" class="text-center">متوسط مرجح</th>
                <th>-</th>
                <th class="text-success"><strong>{{ '%.2f'|format(mix.total_cost) }} د.أ</strong></th>
            </tr>
        </tfoot>
    </table>
</div>
</body>
</html>
