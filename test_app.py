import unittest
import json
from app import app, db, Mix

class TestMixApp(unittest.TestCase):
    def setUp(self):
        """إعداد قاعدة البيانات للاختبار"""
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.app = app.test_client()
        
        with app.app_context():
            db.create_all()
    
    def tearDown(self):
        """تنظيف قاعدة البيانات بعد الاختبار"""
        with app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_index_page_loads(self):
        """اختبار تحميل الصفحة الرئيسية"""
        response = self.app.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn('حساب نسبة الألياف', response.data.decode('utf-8'))
    
    def test_create_mix_success(self):
        """اختبار إنشاء خلطة بنجاح"""
        data = {
            'mix_name': 'خلطة تجريبية',
            'name_select_1': 'شعير',
            'qty_1': '10',
            'fiber_1': '5.5',
            'protein_1': '12.0',
            'energy_1': '2.8',
            'price_1': '2.5'
        }
        response = self.app.post('/', data=data, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn('تم حفظ الخلطة بنجاح', response.data.decode('utf-8'))
    
    def test_create_mix_with_other_material(self):
        """اختبار إنشاء خلطة مع مادة أخرى"""
        data = {
            'mix_name': 'خلطة مع مادة أخرى',
            'name_select_1': 'أخرى',
            'name_1': 'مادة جديدة',
            'qty_1': '5',
            'fiber_1': '3.2',
            'protein_1': '8.5',
            'energy_1': '2.2',
            'price_1': '1.8'
        }
        response = self.app.post('/', data=data, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn('تم حفظ الخلطة بنجاح', response.data.decode('utf-8'))
    
    def test_create_mix_without_name(self):
        """اختبار إنشاء خلطة بدون اسم"""
        data = {
            'mix_name': '',
            'name_select_1': 'شعير',
            'qty_1': '10',
            'fiber_1': '5.5',
            'protein_1': '12.0',
            'energy_1': '2.8',
            'price_1': '2.5'
        }
        response = self.app.post('/', data=data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('يجب إدخال اسم للخلطة', response.data.decode('utf-8'))
    
    def test_create_mix_without_items(self):
        """اختبار إنشاء خلطة بدون مواد"""
        data = {
            'mix_name': 'خلطة فارغة'
        }
        response = self.app.post('/', data=data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('يجب إضافة مادة واحدة على الأقل', response.data.decode('utf-8'))
    
    def test_invalid_numbers(self):
        """اختبار إدخال أرقام غير صحيحة"""
        data = {
            'mix_name': 'خلطة بأرقام خاطئة',
            'name_select_1': 'شعير',
            'qty_1': 'abc',
            'fiber_1': '5.5',
            'protein_1': '12.0',
            'energy_1': '2.8',
            'price_1': '2.5'
        }
        response = self.app.post('/', data=data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('يرجى إدخال أرقام صحيحة', response.data.decode('utf-8'))

    def test_negative_values(self):
        """اختبار إدخال قيم سالبة"""
        data = {
            'mix_name': 'خلطة بقيم سالبة',
            'name_select_1': 'شعير',
            'qty_1': '-5',
            'fiber_1': '5.5',
            'protein_1': '12.0',
            'energy_1': '2.8',
            'price_1': '2.5'
        }
        response = self.app.post('/', data=data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('يجب أن تكون جميع القيم موجبة', response.data.decode('utf-8'))

if __name__ == '__main__':
    unittest.main()
