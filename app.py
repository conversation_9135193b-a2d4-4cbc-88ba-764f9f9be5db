from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
import os
from material_defaults import MATERIAL_DEFAULTS

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

class Mix(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    total_fiber = db.Column(db.Float, nullable=False)
    total_protein = db.Column(db.Float, nullable=False)
    total_energy = db.Column(db.Float, nullable=False)
    total_cost = db.Column(db.Float, nullable=False)
    items = db.Column(db.Text, nullable=False)  # JSON string of items


# قائمة المواد الشائعة
COMMON_MATERIALS = [
    'برسيم حجازي',
    'شعير',
    'ذرة صفراء',
    'نخالة قمح',
    'كسبة فول الصويا',
    'كسبة بذرة القطن',
    'دريس رودس',
    'تبن قمح',
    'علف مركز',
    'ملح طعام',
    'حجر جيري',
    'أخرى'
]

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        mix_name = request.form.get('mix_name')
        items = []
        total_fiber = 0
        total_protein = 0
        total_energy = 0
        total_cost = 0
        for i in range(1, 11):  # Up to 10 items
            name_select = request.form.get(f'name_select_{i}')
            name_input = request.form.get(f'name_{i}')
            # إذا اختار "أخرى"، استخدم الحقل النصي، وإلا استخدم من القائمة
            name = name_input if name_select == 'أخرى' else name_select
            qty = request.form.get(f'qty_{i}')
            fiber = request.form.get(f'fiber_{i}')
            protein = request.form.get(f'protein_{i}')
            energy = request.form.get(f'energy_{i}')
            price = request.form.get(f'price_{i}')
            if name and qty and fiber and protein and energy and price:
                try:
                    qty = float(qty)
                    fiber = float(fiber)
                    protein = float(protein)
                    energy = float(energy)
                    price = float(price)
                    # التحقق من أن القيم موجبة
                    if qty <= 0 or fiber < 0 or protein < 0 or energy < 0 or price < 0:
                        flash(f'يجب أن تكون جميع القيم موجبة للمادة: {name}', 'error')
                        continue
                    items.append({
                        'name': name,
                        'qty': qty,
                        'fiber': fiber,
                        'protein': protein,
                        'energy': energy,
                        'price': price
                    })
                    total_fiber += (fiber * qty)
                    total_protein += (protein * qty)
                    total_energy += (energy * qty)
                    total_cost += (price * qty)
                except ValueError:
                    flash(f'يرجى إدخال أرقام صحيحة للمادة: {name}', 'error')
                    continue
        # التحقق من وجود عناصر في الخلطة
        if not items:
            flash('يجب إضافة مادة واحدة على الأقل للخلطة', 'error')
            return render_template('index.html', mixes=Mix.query.order_by(Mix.id.desc()).all(), common_materials=COMMON_MATERIALS, material_defaults=MATERIAL_DEFAULTS)

        # التحقق من وجود اسم للخلطة
        if not mix_name or mix_name.strip() == '':
            flash('يجب إدخال اسم للخلطة', 'error')
            return render_template('index.html', mixes=Mix.query.order_by(Mix.id.desc()).all(), common_materials=COMMON_MATERIALS, material_defaults=MATERIAL_DEFAULTS)

        total_qty = sum([item['qty'] for item in items])
        fiber_percent = (total_fiber / total_qty) if total_qty else 0
        protein_percent = (total_protein / total_qty) if total_qty else 0
        energy_per_kg = (total_energy / total_qty) if total_qty else 0
        # Save to DB
        import json
        try:
            mix = Mix(
                name=mix_name.strip(),
                total_fiber=fiber_percent,
                total_protein=protein_percent,
                total_energy=energy_per_kg,
                total_cost=total_cost,
                items=json.dumps(items, ensure_ascii=False)
            )
            db.session.add(mix)
            db.session.commit()
            flash('تم حفظ الخلطة بنجاح!', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ الخلطة', 'error')
            return render_template('index.html', mixes=Mix.query.order_by(Mix.id.desc()).all(), common_materials=COMMON_MATERIALS, material_defaults=MATERIAL_DEFAULTS)
    mixes = Mix.query.order_by(Mix.id.desc()).all()
    return render_template('index.html', mixes=mixes, common_materials=COMMON_MATERIALS, material_defaults=MATERIAL_DEFAULTS)

@app.route('/mix/<int:mix_id>')
def view_mix(mix_id):
    import json
    mix = Mix.query.get_or_404(mix_id)
    items = json.loads(mix.items)
    return render_template('view_mix.html', mix=mix, items=items)

@app.route('/delete_mix/<int:mix_id>', methods=['POST'])
def delete_mix(mix_id):
    try:
        mix = Mix.query.get_or_404(mix_id)
        db.session.delete(mix)
        db.session.commit()
        flash(f'تم حذف الخلطة "{mix.name}" بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف الخلطة', 'error')
    return redirect(url_for('index'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
