<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>حساب الخلطة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body class="bg-light">
<div class="container py-4">
    <h2 class="mb-4">حساب نسبة الألياف والتكلفة الإجمالية</h2>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    <form method="post" class="card p-3 mb-4">
        <div class="mb-3">
            <label class="form-label">اسم الخلطة</label>
            <input type="text" name="mix_name" class="form-control" required>
        </div>
        <div class="row fw-bold text-center">
            <div class="col-2">المكون</div>
            <div class="col-2">الكمية (كغ)</div>
            <div class="col-2">الألياف (%)</div>
            <div class="col-2">البروتين (%)</div>
            <div class="col-2">الطاقة (ميجا كالوري/كغ)</div>
            <div class="col-2">السعر/كغ (دينار)</div>
        </div>
        {% for i in range(1, 11) %}
        <div class="row mb-2">
            <div class="col-2">
                <select name="name_select_{{i}}" class="form-select" onchange="handleMaterialChange(this, '{{i}}')">
                    <option value="">اختر المكون</option>
                    {% for material in common_materials %}
                    <option value="{{ material }}">{{ material }}</option>
                    {% endfor %}
                </select>
                <input type="text" name="name_{{i}}" class="form-control mt-1 d-none" placeholder="اكتب اسم المكون" />
            </div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="qty_{{i}}" class="form-control" placeholder="0.00"></div>
            <div class="col-2"><input type="number" step="0.01" min="0" max="100" name="fiber_{{i}}" class="form-control" placeholder="0.00"></div>
            <div class="col-2"><input type="number" step="0.01" min="0" max="100" name="protein_{{i}}" class="form-control" placeholder="0.00"></div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="energy_{{i}}" class="form-control" placeholder="0.00"></div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="price_{{i}}" class="form-control" placeholder="0.00"></div>
        </div>
        {% endfor %}
        <button type="submit" class="btn btn-primary mt-3">حساب وحفظ الخلطة</button>
    </form>
    <h4>الخلطات المحفوظة</h4>
    <ul class="list-group">
        {% for mix in mixes %}
        <li class="list-group-item">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-1">{{ mix.name }}</h6>
                <div>
                    <a href="{{ url_for('view_mix', mix_id=mix.id) }}" class="btn btn-sm btn-outline-info ms-2">عرض التفاصيل</a>
                    <a href="{{ url_for('edit_mix', mix_id=mix.id) }}" class="btn btn-sm btn-outline-warning ms-1">تعديل</a>
                    <form method="post" action="{{ url_for('delete_mix', mix_id=mix.id) }}" class="d-inline ms-1" onsubmit="return confirm('هل أنت متأكد من حذف هذه الخلطة؟')">
                        <button type="submit" class="btn btn-sm btn-outline-danger">حذف</button>
                    </form>
                </div>
            </div>
            <div class="row text-muted small mt-2">
                <div class="col-3">الألياف: {{ '%.2f'|format(mix.total_fiber) }}%</div>
                <div class="col-3">البروتين: {{ '%.2f'|format(mix.total_protein) }}%</div>
                <div class="col-3">الطاقة: {{ '%.2f'|format(mix.total_energy) }} م.ك/كغ</div>
                <div class="col-3">التكلفة: {{ '%.2f'|format(mix.total_cost) }} د.أ</div>
            </div>
        </li>
        {% else %}
        <li class="list-group-item">لا توجد خلطات محفوظة بعد.</li>
        {% endfor %}
    </ul>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// القيم الافتراضية للمواد
const materialDefaults = {{ material_defaults|tojson|safe }} || {};

function handleMaterialChange(select, idx) {
    var input = select.parentElement.querySelector('input[name="name_' + idx + '"]');
    var fiberInput = document.querySelector('input[name="fiber_' + idx + '"]');
    var proteinInput = document.querySelector('input[name="protein_' + idx + '"]');
    var energyInput = document.querySelector('input[name="energy_' + idx + '"]');
    var priceInput = document.querySelector('input[name="price_' + idx + '"]');

    if (select.value === 'أخرى') {
        input.classList.remove('d-none');
        input.required = true;
        // مسح القيم عند اختيار "أخرى"
        fiberInput.value = '';
        proteinInput.value = '';
        energyInput.value = '';
        priceInput.value = '';
    } else {
        input.classList.add('d-none');
        input.required = false;
        input.value = '';

        // ملء القيم الافتراضية إذا كانت متوفرة
        if (select.value && materialDefaults[select.value]) {
            var defaults = materialDefaults[select.value];
            fiberInput.value = defaults.fiber || '';
            proteinInput.value = defaults.protein || '';
            energyInput.value = defaults.energy || '';
            priceInput.value = defaults.price || '';
        } else {
            // مسح القيم إذا لم تكن هناك قيم افتراضية
            fiberInput.value = '';
            proteinInput.value = '';
            energyInput.value = '';
            priceInput.value = '';
        }
    }
}

// إضافة تلميح للمستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تلميح أسفل النموذج
    var form = document.querySelector('form');
    var hint = document.createElement('div');
    hint.className = 'alert alert-info mt-2';
    hint.innerHTML = '<i class="fas fa-info-circle"></i> <strong>تلميح:</strong> عند اختيار مادة من القائمة، سيتم ملء القيم الافتراضية تلقائياً. يمكنك تعديل هذه القيم حسب الحاجة.';
    form.appendChild(hint);
});
</script>
</body>
</html>
