#!/usr/bin/env python3
"""
إضافة بيانات تجريبية للتطبيق
"""

import json
from app import app, db, Mix

def add_sample_mixes():
    """إضافة خلطات تجريبية"""
    
    with app.app_context():
        # التحقق من وجود بيانات مسبقاً
        if Mix.query.count() > 0:
            print("توجد بيانات مسبقاً في قاعدة البيانات.")
            return
        
        # خلطة للأبقار الحلوب
        dairy_items = [
            {'name': 'برسيم حجازي', 'qty': 15.0, 'fiber': 25.0, 'protein': 18.0, 'energy': 2.2, 'price': 3.5},
            {'name': 'ذرة صفراء', 'qty': 20.0, 'fiber': 2.5, 'protein': 9.0, 'energy': 3.2, 'price': 2.8},
            {'name': 'كسبة فول الصويا', 'qty': 8.0, 'fiber': 6.0, 'protein': 44.0, 'energy': 2.3, 'price': 4.5},
            {'name': 'نخالة قمح', 'qty': 5.0, 'fiber': 12.0, 'protein': 16.0, 'energy': 2.0, 'price': 1.8},
            {'name': 'ملح طعام', 'qty': 0.5, 'fiber': 0.0, 'protein': 0.0, 'energy': 0.0, 'price': 1.0}
        ]
        
        total_qty = sum(item['qty'] for item in dairy_items)
        total_fiber = sum(item['fiber'] * item['qty'] for item in dairy_items) / total_qty
        total_protein = sum(item['protein'] * item['qty'] for item in dairy_items) / total_qty
        total_energy = sum(item['energy'] * item['qty'] for item in dairy_items) / total_qty
        total_cost = sum(item['price'] * item['qty'] for item in dairy_items)
        
        dairy_mix = Mix(
            name='خلطة الأبقار الحلوب',
            total_fiber=total_fiber,
            total_protein=total_protein,
            total_energy=total_energy,
            total_cost=total_cost,
            items=json.dumps(dairy_items, ensure_ascii=False)
        )
        
        # خلطة للأغنام
        sheep_items = [
            {'name': 'شعير', 'qty': 25.0, 'fiber': 5.5, 'protein': 12.0, 'energy': 2.8, 'price': 2.2},
            {'name': 'دريس رودس', 'qty': 15.0, 'fiber': 35.0, 'protein': 8.0, 'energy': 1.8, 'price': 1.5},
            {'name': 'كسبة بذرة القطن', 'qty': 5.0, 'fiber': 12.0, 'protein': 38.0, 'energy': 2.0, 'price': 3.2},
            {'name': 'نخالة قمح', 'qty': 3.0, 'fiber': 12.0, 'protein': 16.0, 'energy': 2.0, 'price': 1.8},
            {'name': 'حجر جيري', 'qty': 0.3, 'fiber': 0.0, 'protein': 0.0, 'energy': 0.0, 'price': 0.5}
        ]
        
        total_qty = sum(item['qty'] for item in sheep_items)
        total_fiber = sum(item['fiber'] * item['qty'] for item in sheep_items) / total_qty
        total_protein = sum(item['protein'] * item['qty'] for item in sheep_items) / total_qty
        total_energy = sum(item['energy'] * item['qty'] for item in sheep_items) / total_qty
        total_cost = sum(item['price'] * item['qty'] for item in sheep_items)
        
        sheep_mix = Mix(
            name='خلطة الأغنام',
            total_fiber=total_fiber,
            total_protein=total_protein,
            total_energy=total_energy,
            total_cost=total_cost,
            items=json.dumps(sheep_items, ensure_ascii=False)
        )
        
        # خلطة اقتصادية
        economic_items = [
            {'name': 'تبن قمح', 'qty': 30.0, 'fiber': 45.0, 'protein': 4.0, 'energy': 1.2, 'price': 0.8},
            {'name': 'شعير', 'qty': 15.0, 'fiber': 5.5, 'protein': 12.0, 'energy': 2.8, 'price': 2.2},
            {'name': 'نخالة قمح', 'qty': 8.0, 'fiber': 12.0, 'protein': 16.0, 'energy': 2.0, 'price': 1.8},
            {'name': 'علف مركز', 'qty': 5.0, 'fiber': 8.0, 'protein': 16.0, 'energy': 2.5, 'price': 3.0}
        ]
        
        total_qty = sum(item['qty'] for item in economic_items)
        total_fiber = sum(item['fiber'] * item['qty'] for item in economic_items) / total_qty
        total_protein = sum(item['protein'] * item['qty'] for item in economic_items) / total_qty
        total_energy = sum(item['energy'] * item['qty'] for item in economic_items) / total_qty
        total_cost = sum(item['price'] * item['qty'] for item in economic_items)
        
        economic_mix = Mix(
            name='خلطة اقتصادية',
            total_fiber=total_fiber,
            total_protein=total_protein,
            total_energy=total_energy,
            total_cost=total_cost,
            items=json.dumps(economic_items, ensure_ascii=False)
        )
        
        # إضافة الخلطات إلى قاعدة البيانات
        try:
            db.session.add(dairy_mix)
            db.session.add(sheep_mix)
            db.session.add(economic_mix)
            db.session.commit()
            print("تم إضافة الخلطات التجريبية بنجاح!")
            print(f"- خلطة الأبقار الحلوب: ألياف {total_fiber:.1f}%, بروتين {dairy_mix.total_protein:.1f}%, طاقة {dairy_mix.total_energy:.1f} م.ك/كغ")
            print(f"- خلطة الأغنام: ألياف {sheep_mix.total_fiber:.1f}%, بروتين {sheep_mix.total_protein:.1f}%, طاقة {sheep_mix.total_energy:.1f} م.ك/كغ")
            print(f"- خلطة اقتصادية: ألياف {economic_mix.total_fiber:.1f}%, بروتين {economic_mix.total_protein:.1f}%, طاقة {economic_mix.total_energy:.1f} م.ك/كغ")
        except Exception as e:
            print(f"حدث خطأ أثناء إضافة البيانات: {e}")
            db.session.rollback()

if __name__ == '__main__':
    add_sample_mixes()
