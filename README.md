# تطبيق حساب الخلطة العلفية

تطبيق ويب شامل لحساب القيم الغذائية والتكلفة الإجمالية للخلطات العلفية.

## المميزات

- ✅ حساب شامل للقيم الغذائية: **الألياف، البروتين، والطاقة**
- ✅ حساب التكلفة الإجمالية للخلطة
- ✅ قائمة منسدلة بالمواد العلفية الشائعة مع إمكانية إضافة مواد جديدة
- ✅ التحقق الشامل من صحة البيانات المدخلة
- ✅ حفظ الخلطات في قاعدة البيانات مع جميع التفاصيل
- ✅ عرض تفاصيل الخلطات مع ملخص مرئي للقيم الغذائية
- ✅ حذف الخلطات غير المرغوب فيها مع تأكيد الأمان
- ✅ واجهة مستخدم باللغة العربية مع دعم RTL كامل
- ✅ رسائل تنبيه واضحة للأخطاء والنجاح
- ✅ تصميم متجاوب يعمل على جميع الأجهزة

## المتطلبات

- Python 3.7+
- Flask
- Flask-SQLAlchemy
- Bootstrap-Flask

## التثبيت

1. استنسخ المشروع أو حمل الملفات
2. قم بتثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

## تشغيل التطبيق

```bash
python app.py
```

سيعمل التطبيق على `http://localhost:5000`

## الاستخدام

### إضافة خلطة جديدة

1. أدخل اسم الخلطة
2. لكل مادة علفية:
   - اختر المادة من القائمة المنسدلة أو اختر "أخرى" لإدخال مادة جديدة
   - أدخل الكمية بالكيلوغرام
   - أدخل نسبة الألياف (%)
   - أدخل نسبة البروتين (%)
   - أدخل محتوى الطاقة (ميجا كالوري/كغ)
   - أدخل السعر لكل كيلوغرام
3. اضغط "حساب وحفظ الخلطة"

سيقوم التطبيق بحساب:
- متوسط نسبة الألياف المرجح للخلطة
- متوسط نسبة البروتين المرجح للخلطة
- متوسط محتوى الطاقة المرجح للخلطة
- التكلفة الإجمالية للخلطة

### عرض الخلطات المحفوظة

- ستظهر جميع الخلطات في قائمة أسفل النموذج مع ملخص القيم الغذائية
- اضغط "عرض التفاصيل" لرؤية:
  - ملخص مرئي للقيم الغذائية (الألياف، البروتين، الطاقة، التكلفة)
  - جدول تفصيلي بجميع المواد وقيمها الغذائية
  - التكلفة الجزئية لكل مادة
  - المجاميع والمتوسطات المرجحة
- اضغط "حذف" لحذف خلطة (مع تأكيد الأمان)

## المواد المتاحة مسبقاً

- برسيم حجازي
- شعير
- ذرة صفراء
- نخالة قمح
- كسبة فول الصويا
- كسبة بذرة القطن
- دريس رودس
- تبن قمح
- علف مركز
- ملح طعام
- حجر جيري

## التحقق من صحة البيانات

التطبيق يتحقق من:
- وجود اسم للخلطة
- وجود مادة واحدة على الأقل مع جميع قيمها
- صحة الأرقام المدخلة لجميع الحقول
- أن تكون جميع القيم موجبة (الكمية، الألياف، البروتين، الطاقة، السعر)
- أن تكون نسب الألياف والبروتين بين 0-100%
- عرض رسائل خطأ واضحة ومحددة لكل نوع من المشاكل

## تشغيل الاختبارات

```bash
python test_app.py
```

## هيكل المشروع

```
├── app.py              # التطبيق الرئيسي
├── requirements.txt    # المتطلبات
├── test_app.py        # ملف الاختبارات
├── README.md          # هذا الملف
├── templates/         # قوالب HTML
│   ├── index.html     # الصفحة الرئيسية
│   └── view_mix.html  # صفحة عرض تفاصيل الخلطة
└── instance/          # قاعدة البيانات (تُنشأ تلقائياً)
    └── database.db
```

## التطوير المستقبلي

- إضافة إمكانية تعديل الخلطات
- إضافة إمكانية تصدير البيانات
- إضافة إحصائيات ومخططات
- إضافة نظام مستخدمين
- إضافة إمكانية البحث والفلترة
