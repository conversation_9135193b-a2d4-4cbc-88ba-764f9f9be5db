<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل الخلطة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body class="bg-light">
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <a href="{{ url_for('view_mix', mix_id=mix.id) }}" class="btn btn-secondary">&larr; رجوع</a>
        <h2>تعديل الخلطة: {{ mix.name }}</h2>
    </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    <form method="post" class="card p-3 mb-4">
        <div class="mb-3">
            <label class="form-label">اسم الخلطة</label>
            <input type="text" name="mix_name" class="form-control" value="{{ mix.name }}" required>
        </div>
        <div class="row fw-bold text-center">
            <div class="col-2">المكون</div>
            <div class="col-2">الكمية (كغ)</div>
            <div class="col-2">الألياف (%)</div>
            <div class="col-2">البروتين (%)</div>
            <div class="col-2">الطاقة (ميجا كالوري/كغ)</div>
            <div class="col-2">السعر/كغ (دينار)</div>
        </div>
        {% for i in range(1, 11) %}
        <div class="row mb-2">
            <div class="col-2">
                <select name="name_select_{{i}}" class="form-select" onchange="handleMaterialChange(this, '{{i}}')">
                    <option value="">اختر المكون</option>
                    {% for material in common_materials %}
                    <option value="{{ material }}" {% if items and i <= items|length and items[i-1].name == material %}selected{% endif %}>{{ material }}</option>
                    {% endfor %}
                </select>
                <input type="text" name="name_{{i}}" class="form-control mt-1" placeholder="اسم المكون" 
                       {% if items and i <= items|length and items[i-1].name not in common_materials %}value="{{ items[i-1].name }}"{% endif %}
                       style="{% if items and i <= items|length and items[i-1].name in common_materials %}display: none;{% endif %}">
            </div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="qty_{{i}}" class="form-control" placeholder="0.00" 
                                     {% if items and i <= items|length %}value="{{ '%.2f'|format(items[i-1].qty) }}"{% endif %}></div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="fiber_{{i}}" class="form-control" placeholder="0.00" 
                                     {% if items and i <= items|length %}value="{{ '%.2f'|format(items[i-1].fiber) }}"{% endif %}></div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="protein_{{i}}" class="form-control" placeholder="0.00" 
                                     {% if items and i <= items|length %}value="{{ '%.2f'|format(items[i-1].protein) }}"{% endif %}></div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="energy_{{i}}" class="form-control" placeholder="0.00" 
                                     {% if items and i <= items|length %}value="{{ '%.2f'|format(items[i-1].energy) }}"{% endif %}></div>
            <div class="col-2"><input type="number" step="0.01" min="0" name="price_{{i}}" class="form-control" placeholder="0.00" 
                                     {% if items and i <= items|length %}value="{{ '%.2f'|format(items[i-1].price) }}"{% endif %}></div>
        </div>
        {% endfor %}
        <div class="d-flex gap-2 mt-3">
            <button type="submit" class="btn btn-success">💾 حفظ التعديلات</button>
            <a href="{{ url_for('view_mix', mix_id=mix.id) }}" class="btn btn-outline-secondary">إلغاء</a>
        </div>
    </form>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// القيم الافتراضية للمواد
const materialDefaults = {{ material_defaults|tojson|safe }} || {};

function handleMaterialChange(select, idx) {
    var input = select.parentElement.querySelector('input[name="name_' + idx + '"]');
    var fiberInput = document.querySelector('input[name="fiber_' + idx + '"]');
    var proteinInput = document.querySelector('input[name="protein_' + idx + '"]');
    var energyInput = document.querySelector('input[name="energy_' + idx + '"]');
    var priceInput = document.querySelector('input[name="price_' + idx + '"]');
    
    if (select.value === 'أخرى') {
        input.style.display = 'block';
        input.focus();
    } else {
        input.style.display = 'none';
        input.value = '';
        
        // تطبيق القيم الافتراضية إذا كانت متوفرة
        if (select.value && materialDefaults[select.value]) {
            var defaults = materialDefaults[select.value];
            if (fiberInput && defaults.fiber !== undefined) fiberInput.value = defaults.fiber;
            if (proteinInput && defaults.protein !== undefined) proteinInput.value = defaults.protein;
            if (energyInput && defaults.energy !== undefined) energyInput.value = defaults.energy;
            if (priceInput && defaults.price !== undefined) priceInput.value = defaults.price;
        }
    }
}

// تطبيق حالة "أخرى" للمواد المحملة مسبقاً
document.addEventListener('DOMContentLoaded', function() {
    {% for i in range(1, 11) %}
    {% if items and i <= items|length and items[i-1].name not in common_materials %}
    var select{{i}} = document.querySelector('select[name="name_select_{{i}}"]');
    if (select{{i}}) {
        select{{i}}.value = 'أخرى';
        handleMaterialChange(select{{i}}, '{{i}}');
    }
    {% endif %}
    {% endfor %}
});
</script>
</body>
</html>
