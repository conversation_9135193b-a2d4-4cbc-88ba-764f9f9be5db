#!/usr/bin/env python3
"""
Script لتحديث قاعدة البيانات لإضافة حقول البروتين والطاقة
"""

import sqlite3
import os
from app import app, db

def migrate_database():
    """تحديث قاعدة البيانات لإضافة الحقول الجديدة"""
    
    # مسار قاعدة البيانات
    db_path = 'instance/database.db'
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة. سيتم إنشاؤها من جديد.")
        with app.app_context():
            db.create_all()
        print("تم إنشاء قاعدة البيانات بنجاح!")
        return
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # التحقق من وجود الحقول الجديدة
        cursor.execute("PRAGMA table_info(mix)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة حقل البروتين إذا لم يكن موجوداً
        if 'total_protein' not in columns:
            print("إضافة حقل البروتين...")
            cursor.execute("ALTER TABLE mix ADD COLUMN total_protein REAL DEFAULT 0.0")
            print("تم إضافة حقل البروتين بنجاح!")
        
        # إضافة حقل الطاقة إذا لم يكن موجوداً
        if 'total_energy' not in columns:
            print("إضافة حقل الطاقة...")
            cursor.execute("ALTER TABLE mix ADD COLUMN total_energy REAL DEFAULT 0.0")
            print("تم إضافة حقل الطاقة بنجاح!")
        
        # حفظ التغييرات
        conn.commit()
        print("تم تحديث قاعدة البيانات بنجاح!")
        
        # عرض هيكل الجدول المحدث
        cursor.execute("PRAGMA table_info(mix)")
        columns = cursor.fetchall()
        print("\nهيكل الجدول المحدث:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
            
    except Exception as e:
        print(f"حدث خطأ أثناء تحديث قاعدة البيانات: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    migrate_database()
